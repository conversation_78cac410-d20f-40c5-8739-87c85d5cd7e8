# 使用python脚本按以下条件生成代码
# 1. 遍历扫描：E:\ShareCache\曾星\联塑运维知识库-20250605目录下的01-运维系统说明知识库、02-运维操作规范知识库、03-运维管理规范知识库 三个子目录 找到全部文件；
# 2. 跳过扫描三个子目录下的[old]和空目录；
# 3. 遍历文件将文件信息新增或更新到《E:\ShareCache\曾星\联塑运维知识库-20250605\知识库文档登记表.xlsx》的文档登记表sheet
# 4. 知识库文档登记表包括以下字段：
#   * 知识库：对应01-运维系统说明知识库、02-运维操作规范知识库、03-运维管理规范知识库的其中一个；
#   * 文档名称：文件命名规范《文档名称_作者名.文件格式》，如系统运维部各团队的职能范围描述_曾星.md
# 	* 文档路径：如：03-运维管理规范知识库\03-组织架构
#   * 文档维护者: 获取文件名后缀"_作者名"
#   * md5sum：md5生成文件的校验码
#   * 创建日期: 文件的创建时间
#  	* 是否上传知识库： 默认是“否”，上传到ragflow更新该字段
#   * 上传日期： 上传到ragflow更新该字段
