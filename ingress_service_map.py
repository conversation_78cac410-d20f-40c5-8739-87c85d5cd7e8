import json
import subprocess
from typing import List, Dict, Optional, TypedDict
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import argparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ServicePortInfo:
    name: str
    port: str
    target_port: str
    protocol: str

@dataclass
class ServiceInfo:
    name: str
    type: str
    ports: List[ServicePortInfo]

@dataclass
class IngressInfo:
    namespace: str
    ingress_name: str
    host: str
    protocol: str
    path: str
    service_name: str
    service_port: str
    service_details: Optional[ServiceInfo]

def run_kubectl_command(cmd: str) -> Dict:
    """执行kubectl命令并返回JSON输出"""
    try:
        result = subprocess.run(
            cmd, shell=True, check=True, capture_output=True, text=True
        )
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        logger.error(f"Error executing command: {cmd}\n{e.stderr}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON from command: {cmd}\n{e}")
        return {}

def get_namespaces() -> List[str]:
    """获取所有命名空间列表"""
    cmd = "kubectl get namespaces -o json"
    data = run_kubectl_command(cmd)
    return [ns["metadata"]["name"] for ns in data.get("items", [])]

def get_ingress_info(namespace: str) -> List[Dict]:
    """获取指定命名空间下的Ingress信息"""
    cmd = f"kubectl get ingress -n {namespace} -o json"
    data = run_kubectl_command(cmd)
    ingress_list = []
    
    for ingress in data.get("items", []):
        ingress_name = ingress["metadata"]["name"]
        rules = ingress.get("spec", {}).get("rules", [])
        tls = ingress.get("spec", {}).get("tls", [])
        
        # 处理TLS域名
        tls_hosts = [host for tls_item in tls for host in tls_item.get("hosts", [])]
        
        for rule in rules:
            host = rule.get("host", "")
            http_paths = rule.get("http", {}).get("paths", [])
            
            for path in http_paths:
                backend = path.get("backend", {})
                service_name = backend.get("service", {}).get("name", "")
                service_port = backend.get("service", {}).get("port", {})
                
                # 处理端口可能是数字或名称的情况
                if isinstance(service_port, dict):
                    service_port_number = service_port.get("number", service_port.get("name", ""))
                else:
                    service_port_number = service_port
                
                # 确定协议
                protocol = "HTTPS" if host in tls_hosts or tls_hosts else "HTTP"
                
                ingress_info = {
                    "namespace": namespace,
                    "ingress_name": ingress_name,
                    "host": host,
                    "protocol": protocol,
                    "path": path.get("path", "/"),
                    "service_name": service_name,
                    "service_port": service_port_number,
                }
                ingress_list.append(ingress_info)
    
    return ingress_list

def get_service_info(namespace: str, service_name: str) -> Dict:
    """获取Service的详细信息"""
    cmd = f"kubectl get service {service_name} -n {namespace} -o json"
    data = run_kubectl_command(cmd)
    
    if not data:
        return {}
    
    ports_info = []
    for port in data.get("spec", {}).get("ports", []):
        ports_info.append({
            "name": port.get("name", ""),
            "port": port.get("port", ""),
            "target_port": port.get("targetPort", ""),
            "protocol": port.get("protocol", "TCP"),
        })
    
    return {
        "service_name": service_name,
        "service_type": data.get("spec", {}).get("type", ""),
        "ports": ports_info,
    }

def process_namespace(namespace: str) -> List[IngressInfo]:
    """处理单个命名空间并返回Ingress信息列表"""
    ingress_list = get_ingress_info(namespace)
    results = []
    
    for ingress in ingress_list:
        service_info = None
        if ingress["service_name"]:
            service_data = get_service_info(namespace, ingress["service_name"])
            if service_data:
                ports = [
                    ServicePortInfo(
                        name=p.get("name", ""),
                        port=str(p.get("port", "")),
                        target_port=str(p.get("targetPort", "")),
                        protocol=p.get("protocol", "TCP")
                    ) for p in service_data.get("ports", [])
                ]
                service_info = ServiceInfo(
                    name=ingress["service_name"],
                    type=service_data.get("service_type", ""),
                    ports=ports
                )
        
        results.append(IngressInfo(
            namespace=namespace,
            ingress_name=ingress["ingress_name"],
            host=ingress["host"],
            protocol=ingress["protocol"],
            path=ingress["path"],
            service_name=ingress["service_name"],
            service_port=str(ingress["service_port"]),
            service_details=service_info
        ))
    
    return results

def print_results(results: List[IngressInfo]):
    """格式化打印结果"""
    headers = [
        "Namespace", "Ingress Name", "Host",
        "Protocol", "Path", "Service Name",
        "Service Port", "Service Protocols"
    ]
    header_line = "\t".join(headers)
    print(header_line)
    print("-" * len(header_line.expandtabs()))
    
    for item in results:
        protocols = ", ".join(
            p.protocol for p in item.service_details.ports
        ) if item.service_details else ""
        
        print(
            f"{item.namespace}\t"
            f"{item.ingress_name}\t"
            f"{item.host}\t"
            f"{item.protocol}\t"
            f"{item.path}\t"
            f"{item.service_name}\t"
            f"{item.service_port}\t"
            f"{protocols}"
        )

def dataclass_to_dict(obj):
    """将dataclass对象转换为可序列化的字典"""
    if isinstance(obj, (str, int, float, bool, type(None))):
        return obj
    elif isinstance(obj, list):
        return [dataclass_to_dict(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: dataclass_to_dict(v) for k, v in obj.items()}
    elif hasattr(obj, "__dataclass_fields__"):
        return {field: dataclass_to_dict(getattr(obj, field))
               for field in obj.__dataclass_fields__}
    return obj

def main():
    parser = argparse.ArgumentParser(
        description="Kubernetes Ingress和Service映射关系查询工具"
    )
    parser.add_argument(
        "-o", "--output",
        help="输出JSON文件路径",
        default="ingress_service_mapping.json"
    )
    parser.add_argument(
        "-j", "--json-only",
        help="仅输出JSON文件，不打印表格",
        action="store_true"
    )
    args = parser.parse_args()
    
    all_namespaces = get_namespaces()
    results = []
    
    # 使用线程池并行处理命名空间
    with ThreadPoolExecutor() as executor:
        namespace_results = executor.map(process_namespace, all_namespaces)
        for ns_result in namespace_results:
            results.extend(ns_result)
    
    if not args.json_only:
        print_results(results)
    
    with open(args.output, "w") as f:
        json.dump([dataclass_to_dict(r) for r in results], f, indent=2)

if __name__ == "__main__":
    main()
