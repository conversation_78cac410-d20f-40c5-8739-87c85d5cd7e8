#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库文档收集脚本

功能：
1. 遍历扫描指定目录下的三个知识库子目录，找到全部文件
2. 跳过[old]目录和空目录
3. 将文件信息新增或更新到Excel登记表中

作者：AI Assistant
创建日期：2025-01-08
"""

import os
import hashlib
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_collection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class KnowledgeBaseCollector:
    """知识库文档收集器"""

    def __init__(self, base_path: str, excel_path: str):
        """
        初始化收集器

        Args:
            base_path: 知识库根目录路径
            excel_path: Excel登记表路径
        """
        self.base_path = Path(base_path)
        self.excel_path = Path(excel_path)
        self.knowledge_bases = [
            "01-运维系统说明知识库",
            "02-运维操作规范知识库",
            "03-运维管理规范知识库"
        ]
        self.sheet_name = "文档登记表"
        self.columns = [
            "知识库", "文档名称", "文档路径", "文档维护者",
            "md5sum", "创建日期", "是否上传知识库", "上传日期"
        ]

    def calculate_md5(self, file_path: Path) -> str:
        """
        计算文件的MD5校验码

        Args:
            file_path: 文件路径

        Returns:
            MD5校验码字符串
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算MD5失败 {file_path}: {e}")
            return ""

    def extract_author_from_filename(self, filename: str) -> str:
        """
        从文件名中提取作者名

        文件命名规范：文档名称_作者名.文件格式

        Args:
            filename: 文件名

        Returns:
            作者名，如果提取失败返回空字符串
        """
        try:
            # 移除文件扩展名
            name_without_ext = Path(filename).stem
            # 查找最后一个下划线后的内容作为作者名
            match = re.search(r'_([^_]+)$', name_without_ext)
            if match:
                return match.group(1)
            else:
                logger.warning(f"无法从文件名提取作者: {filename}")
                return ""
        except Exception as e:
            logger.error(f"提取作者名失败 {filename}: {e}")
            return ""

    def get_file_creation_time(self, file_path: Path) -> str:
        """
        获取文件创建时间

        Args:
            file_path: 文件路径

        Returns:
            格式化的创建时间字符串
        """
        try:
            # 在Windows上使用st_ctime，在Unix上使用st_birthtime（如果可用）
            stat_info = file_path.stat()
            if hasattr(stat_info, 'st_birthtime'):
                # macOS/BSD系统
                creation_time = stat_info.st_birthtime
            else:
                # Windows/Linux系统
                creation_time = stat_info.st_ctime

            return datetime.fromtimestamp(creation_time).strftime(
                '%Y-%m-%d %H:%M:%S'
            )
        except Exception as e:
            logger.error(f"获取文件创建时间失败 {file_path}: {e}")
            return ""

    def should_skip_directory(self, dir_path: Path) -> bool:
        """
        判断是否应该跳过目录

        Args:
            dir_path: 目录路径

        Returns:
            True表示跳过，False表示不跳过
        """
        # 跳过[old]目录
        if dir_path.name == "[old]":
            return True

        # 跳过空目录
        try:
            if not any(dir_path.iterdir()):
                return True
        except PermissionError:
            logger.warning(f"无权限访问目录: {dir_path}")
            return True

        return False

    def scan_files(self) -> List[Dict[str, str]]:
        """
        扫描知识库目录中的所有文件

        Returns:
            文件信息列表
        """
        file_records = []

        for kb_name in self.knowledge_bases:
            kb_path = self.base_path / kb_name

            if not kb_path.exists():
                logger.warning(f"知识库目录不存在: {kb_path}")
                continue

            logger.info(f"开始扫描知识库: {kb_name}")

            # 递归遍历目录
            for root, dirs, files in os.walk(kb_path):
                root_path = Path(root)

                # 过滤要跳过的子目录
                dirs[:] = [
                    d for d in dirs
                    if not self.should_skip_directory(root_path / d)
                ]

                # 处理当前目录中的文件
                for file in files:
                    file_path = root_path / file

                    # 计算相对路径（相对于知识库根目录）
                    try:
                        relative_path = file_path.relative_to(self.base_path)
                        doc_path = str(relative_path.parent)
                    except ValueError:
                        logger.error(f"无法计算相对路径: {file_path}")
                        continue

                    # 提取文档信息
                    author = self.extract_author_from_filename(file)
                    md5_hash = self.calculate_md5(file_path)
                    creation_time = self.get_file_creation_time(file_path)

                    record = {
                        "知识库": kb_name,
                        "文档名称": file,
                        "文档路径": doc_path,
                        "文档维护者": author,
                        "md5sum": md5_hash,
                        "创建日期": creation_time,
                        "是否上传知识库": "否",
                        "上传日期": ""
                    }

                    file_records.append(record)
                    logger.debug(f"添加文件记录: {file}")

        logger.info(f"总共扫描到 {len(file_records)} 个文件")
        return file_records

    def load_existing_excel(self) -> Optional[pd.DataFrame]:
        """
        加载现有的Excel文件

        Returns:
            现有的DataFrame，如果文件不存在则返回None
        """
        try:
            if self.excel_path.exists():
                df = pd.read_excel(
                    self.excel_path,
                    sheet_name=self.sheet_name,
                    engine='openpyxl'
                )
                logger.info(f"成功加载现有Excel文件，包含 {len(df)} 条记录")
                return df
            else:
                logger.info("Excel文件不存在，将创建新文件")
                return None
        except Exception as e:
            logger.error(f"加载Excel文件失败: {e}")
            return None

    def create_unique_key(self, record: Dict[str, str]) -> str:
        """
        为记录创建唯一键，用于判断是否为同一文件

        Args:
            record: 文件记录

        Returns:
            唯一键字符串
        """
        return f"{record['知识库']}|{record['文档路径']}|{record['文档名称']}"

    def merge_records(
        self,
        existing_df: Optional[pd.DataFrame],
        new_records: List[Dict[str, str]]
    ) -> pd.DataFrame:
        """
        合并现有记录和新记录

        Args:
            existing_df: 现有的DataFrame
            new_records: 新扫描的记录列表

        Returns:
            合并后的DataFrame
        """
        # 创建新记录的DataFrame
        new_df = pd.DataFrame(new_records)

        if existing_df is None or existing_df.empty:
            logger.info("没有现有记录，直接使用新记录")
            return new_df

        # 确保列顺序一致
        existing_df = existing_df.reindex(columns=self.columns, fill_value="")
        new_df = new_df.reindex(columns=self.columns, fill_value="")

        # 为现有记录和新记录创建唯一键
        existing_keys = set()
        for _, row in existing_df.iterrows():
            key = self.create_unique_key(row.to_dict())
            existing_keys.add(key)

        # 过滤出真正的新记录和需要更新的记录
        records_to_add = []
        records_to_update = []

        for record in new_records:
            key = self.create_unique_key(record)
            if key in existing_keys:
                # 检查是否需要更新（比较MD5）
                existing_record = existing_df[
                    (existing_df['知识库'] == record['知识库']) &
                    (existing_df['文档路径'] == record['文档路径']) &
                    (existing_df['文档名称'] == record['文档名称'])
                ]

                if not existing_record.empty:
                    existing_md5 = existing_record.iloc[0]['md5sum']
                    if existing_md5 != record['md5sum']:
                        # MD5不同，需要更新
                        records_to_update.append(record)
                        logger.info(f"文件已更新，需要更新记录: {record['文档名称']}")
            else:
                # 新文件
                records_to_add.append(record)

        # 更新现有记录
        for update_record in records_to_update:
            mask = (
                (existing_df['知识库'] == update_record['知识库']) &
                (existing_df['文档路径'] == update_record['文档路径']) &
                (existing_df['文档名称'] == update_record['文档名称'])
            )
            # 只更新特定字段，保留上传状态
            existing_df.loc[mask, 'md5sum'] = update_record['md5sum']
            existing_df.loc[mask, '创建日期'] = update_record['创建日期']
            existing_df.loc[mask, '文档维护者'] = update_record['文档维护者']

        # 添加新记录
        if records_to_add:
            add_df = pd.DataFrame(records_to_add)
            result_df = pd.concat([existing_df, add_df], ignore_index=True)
            logger.info(f"添加了 {len(records_to_add)} 条新记录")
        else:
            result_df = existing_df
            logger.info("没有新记录需要添加")

        logger.info(f"更新了 {len(records_to_update)} 条记录")
        return result_df

    def save_to_excel(self, df: pd.DataFrame) -> bool:
        """
        保存DataFrame到Excel文件

        Args:
            df: 要保存的DataFrame

        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            self.excel_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用openpyxl引擎保存，支持中文
            with pd.ExcelWriter(
                self.excel_path,
                engine='openpyxl',
                mode='w'
            ) as writer:
                df.to_excel(
                    writer,
                    sheet_name=self.sheet_name,
                    index=False,
                    encoding='utf-8'
                )

            logger.info(f"成功保存Excel文件: {self.excel_path}")
            logger.info(f"总共保存了 {len(df)} 条记录")
            return True

        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            return False

    def run(self) -> bool:
        """
        运行文档收集流程

        Returns:
            执行是否成功
        """
        try:
            logger.info("开始执行知识库文档收集任务")

            # 验证路径
            if not self.base_path.exists():
                logger.error(f"知识库根目录不存在: {self.base_path}")
                return False

            # 扫描文件
            logger.info("开始扫描文件...")
            new_records = self.scan_files()

            if not new_records:
                logger.warning("没有扫描到任何文件")
                return False

            # 加载现有Excel文件
            logger.info("加载现有Excel文件...")
            existing_df = self.load_existing_excel()

            # 合并记录
            logger.info("合并记录...")
            final_df = self.merge_records(existing_df, new_records)

            # 保存到Excel
            logger.info("保存到Excel文件...")
            success = self.save_to_excel(final_df)

            if success:
                logger.info("知识库文档收集任务完成")
                return True
            else:
                logger.error("保存Excel文件失败")
                return False

        except Exception as e:
            logger.error(f"执行过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    # 配置路径
    base_path = r"E:\ShareCache\曾星\联塑运维知识库-20250605"
    excel_path = r"E:\ShareCache\曾星\联塑运维知识库-20250605\知识库文档登记表.xlsx"

    # 创建收集器实例
    collector = KnowledgeBaseCollector(base_path, excel_path)

    # 执行收集任务
    success = collector.run()

    if success:
        print("✅ 知识库文档收集完成！")
        print(f"📊 结果已保存到: {excel_path}")
    else:
        print("❌ 知识库文档收集失败，请检查日志文件")

    return success


if __name__ == "__main__":
    main()
