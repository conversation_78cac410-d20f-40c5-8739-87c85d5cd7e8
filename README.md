# 知识库文档收集脚本

这是一个用于自动收集和管理知识库文档的Python脚本，能够扫描指定目录下的文档文件，并将文档信息记录到Excel登记表中。

## 功能特性

- 🔍 **自动扫描**：递归扫描三个知识库子目录中的所有文件
- 📋 **Excel管理**：自动创建或更新Excel登记表
- 🔒 **智能过滤**：跳过`[old]`目录和空目录
- 🔄 **增量更新**：支持新增和更新现有记录
- 📊 **MD5校验**：自动计算文件MD5校验码用于变更检测
- 📝 **详细日志**：完整的操作日志记录
- ✅ **flake8兼容**：代码符合Python编码规范

## 目录结构要求

脚本会扫描以下三个知识库目录：
```
E:\ShareCache\曾星\联塑运维知识库-20250605\
├── 01-运维系统说明知识库\
├── 02-运维操作规范知识库\
└── 03-运维管理规范知识库\
```

## 文件命名规范

文档文件应遵循以下命名规范：
```
文档名称_作者名.文件格式
```

示例：
- `系统运维部各团队的职能范围描述_曾星.md`
- `数据库维护手册_张三.docx`
- `网络配置指南_李四.pdf`

## 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 直接运行主脚本

```bash
python knowledge_base_collector.py
```

### 2. 作为模块使用

```python
from knowledge_base_collector import KnowledgeBaseCollector

# 配置路径
base_path = r"E:\ShareCache\曾星\联塑运维知识库-20250605"
excel_path = r"E:\ShareCache\曾星\联塑运维知识库-20250605\知识库文档登记表.xlsx"

# 创建收集器实例
collector = KnowledgeBaseCollector(base_path, excel_path)

# 执行收集任务
success = collector.run()
```

### 3. 运行测试

```bash
python test_collector.py
```

## Excel表格字段说明

生成的Excel文件包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 知识库 | 所属知识库名称 | 01-运维系统说明知识库 |
| 文档名称 | 完整文件名 | 系统架构说明_张三.md |
| 文档路径 | 相对路径 | 01-运维系统说明知识库\01-系统架构 |
| 文档维护者 | 从文件名提取的作者 | 张三 |
| md5sum | 文件MD5校验码 | a1b2c3d4e5f6... |
| 创建日期 | 文件创建时间 | 2025-01-08 14:30:25 |
| 是否上传知识库 | 上传状态 | 否 |
| 上传日期 | 上传时间 | (空) |

## 日志文件

脚本运行时会生成 `file_collection.log` 日志文件，记录详细的执行过程和错误信息。

## 注意事项

1. **路径配置**：请根据实际情况修改脚本中的路径配置
2. **权限要求**：确保脚本有读取源目录和写入Excel文件的权限
3. **文件命名**：建议文档文件遵循命名规范以正确提取作者信息
4. **备份建议**：首次运行前建议备份现有Excel文件

## 错误处理

脚本包含完善的错误处理机制：
- 文件读取失败时会记录错误并继续处理其他文件
- Excel文件损坏时会尝试创建新文件
- 权限不足时会跳过相关目录并记录警告

## 自定义配置

可以通过修改 `KnowledgeBaseCollector` 类的初始化参数来自定义：
- 知识库目录名称
- Excel工作表名称
- 字段列表

## 许可证

本脚本仅供内部使用，请勿外传。
