#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库文档收集器测试脚本

用于测试和演示知识库文档收集功能
"""

import tempfile
import os
from pathlib import Path
from knowledge_base_collector import KnowledgeBaseCollector


def create_test_structure():
    """创建测试目录结构"""
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    print(f"创建测试目录: {temp_dir}")
    
    # 创建知识库目录结构
    kb_dirs = [
        "01-运维系统说明知识库/01-系统架构",
        "01-运维系统说明知识库/02-部署指南",
        "02-运维操作规范知识库/01-日常操作",
        "02-运维操作规范知识库/[old]",  # 应该被跳过
        "03-运维管理规范知识库/01-管理制度",
        "03-运维管理规范知识库/02-流程规范"
    ]
    
    for kb_dir in kb_dirs:
        (temp_dir / kb_dir).mkdir(parents=True, exist_ok=True)
    
    # 创建测试文件
    test_files = [
        "01-运维系统说明知识库/01-系统架构/系统架构说明_张三.md",
        "01-运维系统说明知识库/01-系统架构/数据库设计_李四.docx",
        "01-运维系统说明知识库/02-部署指南/部署手册_王五.pdf",
        "02-运维操作规范知识库/01-日常操作/日常巡检_赵六.md",
        "02-运维操作规范知识库/01-日常操作/故障处理_钱七.txt",
        "02-运维操作规范知识库/[old]/旧文档_老版本.md",  # 应该被跳过
        "03-运维管理规范知识库/01-管理制度/管理制度_孙八.md",
        "03-运维管理规范知识库/02-流程规范/审批流程_周九.docx"
    ]
    
    for file_path in test_files:
        full_path = temp_dir / file_path
        # 创建测试文件内容
        content = f"这是测试文件: {file_path}\n创建时间: {os.getcwd()}"
        full_path.write_text(content, encoding='utf-8')
    
    return temp_dir


def test_collector():
    """测试收集器功能"""
    print("🧪 开始测试知识库文档收集器...")
    
    # 创建测试目录结构
    test_dir = create_test_structure()
    excel_path = test_dir / "测试登记表.xlsx"
    
    try:
        # 创建收集器实例
        collector = KnowledgeBaseCollector(str(test_dir), str(excel_path))
        
        # 执行收集
        success = collector.run()
        
        if success:
            print("✅ 测试成功！")
            print(f"📁 测试目录: {test_dir}")
            print(f"📊 Excel文件: {excel_path}")
            
            # 显示结果
            if excel_path.exists():
                import pandas as pd
                df = pd.read_excel(excel_path, sheet_name="文档登记表")
                print(f"\n📋 收集到 {len(df)} 个文件:")
                for _, row in df.iterrows():
                    print(f"  - {row['知识库']}: {row['文档名称']} "
                          f"(作者: {row['文档维护者']})")
        else:
            print("❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理测试目录（可选）
        print(f"\n🗑️  测试完成，测试文件保留在: {test_dir}")
        print("   如需清理，请手动删除该目录")


if __name__ == "__main__":
    test_collector()
